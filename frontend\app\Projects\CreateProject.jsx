import React, { useState, useContext, useRef, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    TextInput,
    StyleSheet,
    StatusBar,
    SafeAreaView,
    Dimensions,
    Animated,
    Easing,
    Image,
    ActivityIndicator,
    Switch,
    Pressable,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { useRouter } from 'expo-router';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import ModalDatePicker from 'react-native-modal-datetime-picker';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';
import { useMutation } from '@tanstack/react-query';
import { createProject } from '../../api/projects/projectApi.jsx';
import Toast from 'react-native-toast-message';

const { height } = Dimensions.get('window');

const validationSchema = Yup.object().shape({
    projectName: Yup.string().required('Project name is required'),
    projectType: Yup.string().required('Project type is required'),
    constructionType: Yup.string().required('Construction type is required'),
    address: Yup.string().required('Address is required'),
    city: Yup.string().required('City is required'),
    state: Yup.string().required('State is required'),
    pincode: Yup.string().required('Pincode is required'),
    minBudget: Yup.number().positive('Minimum budget must be positive'),
    maxBudget: Yup.number()
        .positive('Maximum budget must be positive')
        .test(
            'max-greater-than-min',
            'Maximum budget must be greater than minimum budget',
            function (value) {
                const { minBudget } = this.parent;
                return !minBudget || !value || value > minBudget;
            }
        ),
    floors: Yup.number().positive('Number of floors must be positive'),
    bedrooms: Yup.number().min(0, 'Number of bedrooms cannot be negative'),
    bathrooms: Yup.number().min(0, 'Number of bathrooms cannot be negative'),
});

export default function CreateProject() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const scaleAnim = useRef(new Animated.Value(0.7)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;

    // Date picker states
    const [showStartDatePicker, setShowStartDatePicker] = useState(false);
    const [showCompletionDatePicker, setShowCompletionDatePicker] =
        useState(false);

    const createProjectMutation = useMutation({
        mutationFn: createProject,
        onSuccess: () => {
            Toast.show({
                type: 'success',
                text1: 'Success',
                text2: 'Project created successfully!',
            });
            router.back();
        },
        onError: (error) => {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || 'Failed to create project',
            });
        },
    });

    // Initialize animations
    useEffect(() => {
        Animated.parallel([
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 7,
                tension: 60,
                useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 600,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    // Form options
    const projectTypes = ['Residential', 'Commercial', 'Industrial', 'Other'];
    const constructionTypes = ['New Construction', 'Renovation', 'Extension'];

    // Initial form values
    const initialValues = {
        projectName: '',
        projectType: '',
        constructionType: '',
        address: '',
        city: '',
        state: '',
        pincode: '',
        expectedStartDate: '',
        expectedCompletionDate: '',
        minBudget: '',
        maxBudget: '',
        floors: '',
        bedrooms: '',
        bathrooms: '',
        parkingRequired: false,
        gardenRequired: false,
        vastuCompliance: false,
        brokerAssistanceRequired: false,
        specialInstructions: '',
        additionalFacilities: '',
    };

    // Handle form submission
    const handleSubmit = (values) => {
        const projectData = {
            projectName: values.projectName,
            projectType: values.projectType,
            constructionType: values.constructionType,
            location: {
                address: values.address,
                city: values.city,
                state: values.state,
                pincode: values.pincode,
            },
            expectedStartDate: values.expectedStartDate || undefined,
            expectedCompletionDate: values.expectedCompletionDate || undefined,
            budget: {
                minBudget: values.minBudget
                    ? parseInt(values.minBudget)
                    : undefined,
                maxBudget: values.maxBudget
                    ? parseInt(values.maxBudget)
                    : undefined,
            },
            designPreferences: {
                floors: values.floors ? parseInt(values.floors) : undefined,
                bedrooms: values.bedrooms
                    ? parseInt(values.bedrooms)
                    : undefined,
                bathrooms: values.bathrooms
                    ? parseInt(values.bathrooms)
                    : undefined,
                parkingRequired: values.parkingRequired,
                gardenRequired: values.gardenRequired,
                vastuCompliance: values.vastuCompliance,
            },
            brokerAssistanceRequired: values.brokerAssistanceRequired,
            specialInstructions: values.specialInstructions || undefined,
            additionalFacilities: values.additionalFacilities
                ? values.additionalFacilities
                      .split(',')
                      .map((f) => f.trim())
                      .filter((f) => f)
                : [],
        };

        createProjectMutation.mutate(projectData);
    };

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />

            {/* Background Pattern */}
            <View style={styles.backgroundContainer}>
                <Image
                    source={require('../../assets/images/background.png')}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                    style={styles.backgroundOverlay}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                />
            </View>

            <SafeAreaView style={styles.safeArea}>
                {/* Header */}
                <View style={styles.headerContent}>
                    <BackButton color={theme.WHITE} />
                </View>
                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                >
                    {({
                        values,
                        errors,
                        touched,
                        handleChange,
                        handleBlur,
                        handleSubmit,
                        setFieldValue,
                        setFieldTouched,
                    }) => (
                        <ScrollView
                            style={styles.scrollView}
                            showsVerticalScrollIndicator={false}
                        >
                            <Animated.View
                                style={[
                                    styles.formContainer,
                                    {
                                        transform: [{ scale: scaleAnim }],
                                        opacity: fadeAnim,
                                        shadowColor: theme.SHADOW,
                                        backgroundColor: theme.CARD,
                                    },
                                ]}
                            >
                                <Text
                                    style={[
                                        styles.title,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    Create Project
                                </Text>
                                <Text
                                    style={[
                                        styles.subtitle,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Submit your project preferences to get
                                    matched with the right brokers and
                                    contractors.
                                </Text>
                                {/* Project Name */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Project name
                                        <Text style={{ color: theme.ERROR }}>
                                            *
                                        </Text>
                                    </Text>
                                    <View
                                        style={[
                                            styles.inputContainer,
                                            {
                                                backgroundColor:
                                                    theme.INPUT_BACKGROUND,
                                                borderColor:
                                                    errors.projectName &&
                                                    touched.projectName
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.projectName &&
                                                touched.projectName &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="home-outline"
                                            size={20}
                                            color={
                                                errors.projectName &&
                                                touched.projectName
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    color: theme.TEXT_PRIMARY,
                                                    flex: 1,
                                                },
                                            ]}
                                            placeholder="Enter project name"
                                            placeholderTextColor={
                                                theme.TEXT_PLACEHOLDER
                                            }
                                            value={values.projectName}
                                            onChangeText={handleChange(
                                                'projectName'
                                            )}
                                            onBlur={handleBlur('projectName')}
                                        />
                                    </View>
                                    {errors.projectName &&
                                        touched.projectName && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.projectName}
                                            </Text>
                                        )}
                                </View>

                                {/* Project Type */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Project type
                                        <Text style={{ color: theme.ERROR }}>
                                            *
                                        </Text>
                                    </Text>
                                    <View
                                        style={[
                                            styles.inputContainer,
                                            {
                                                backgroundColor:
                                                    theme.INPUT_BACKGROUND,
                                                borderColor:
                                                    errors.projectType &&
                                                    touched.projectType
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.projectType &&
                                                touched.projectType &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="business-outline"
                                            size={20}
                                            color={
                                                errors.projectType &&
                                                touched.projectType
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <Picker
                                            selectedValue={values.projectType}
                                            onValueChange={(value) =>
                                                setFieldValue(
                                                    'projectType',
                                                    value
                                                )
                                            }
                                            onBlur={() =>
                                                setFieldTouched(
                                                    'projectType',
                                                    true
                                                )
                                            }
                                            dropdownIconColor={theme.PRIMARY}
                                            style={[
                                                styles.picker,
                                                {
                                                    color: values.projectType
                                                        ? theme.TEXT_PRIMARY
                                                        : theme.TEXT_PLACEHOLDER,
                                                },
                                            ]}
                                            accessibilityLabel="Select project type"
                                            testID="project-type-picker"
                                        >
                                            <Picker.Item
                                                label="Select Project type"
                                                value=""
                                                size={12}
                                            />
                                            {projectTypes.map((type) => (
                                                <Picker.Item
                                                    key={type}
                                                    label={type}
                                                    value={type}
                                                    size={12}
                                                />
                                            ))}
                                        </Picker>
                                    </View>
                                    {errors.projectType &&
                                        touched.projectType && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.projectType}
                                            </Text>
                                        )}
                                </View>
                                {/* Construction Type */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Construction type
                                        <Text style={{ color: theme.ERROR }}>
                                            *
                                        </Text>
                                    </Text>

                                    <View
                                        style={[
                                            styles.inputContainer,
                                            {
                                                backgroundColor:
                                                    theme.INPUT_BACKGROUND,
                                                borderColor:
                                                    errors.constructionType &&
                                                    touched.constructionType
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.constructionType &&
                                                touched.constructionType &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="construct-outline"
                                            size={20}
                                            color={
                                                errors.constructionType &&
                                                touched.constructionType
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <Picker
                                            selectedValue={
                                                values.constructionType
                                            }
                                            onValueChange={(value) =>
                                                setFieldValue(
                                                    'constructionType',
                                                    value
                                                )
                                            }
                                            onBlur={() =>
                                                setFieldTouched(
                                                    'constructionType',
                                                    true
                                                )
                                            }
                                            dropdownIconColor={theme.PRIMARY}
                                            style={[
                                                styles.picker,
                                                {
                                                    color: values.constructionType
                                                        ? theme.TEXT_PRIMARY
                                                        : theme.TEXT_PLACEHOLDER,
                                                },
                                            ]}
                                            accessibilityLabel="Select construction type"
                                            testID="construction-type-picker"
                                        >
                                            <Picker.Item
                                                label="Select Construction type"
                                                value=""
                                            />
                                            {constructionTypes.map((type) => (
                                                <Picker.Item
                                                    key={type}
                                                    label={type}
                                                    value={type}
                                                    size={12}
                                                />
                                            ))}
                                        </Picker>
                                    </View>
                                    {errors.constructionType &&
                                        touched.constructionType && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.constructionType}
                                            </Text>
                                        )}
                                </View>
                                {/* Location Details Section */}
                                <View style={styles.sectionHeader}>
                                    <MaterialIcons
                                        name="location-on"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.sectionTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Location Details
                                    </Text>
                                </View>

                                {/* Address */}
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Address
                                    <Text style={{ color: theme.ERROR }}>
                                        *
                                    </Text>
                                </Text>
                                <View
                                    style={[
                                        styles.inputContainer,
                                        styles.addressInputContainer,
                                        {
                                            backgroundColor:
                                                theme.INPUT_BACKGROUND,
                                            borderColor:
                                                errors.address &&
                                                touched.address
                                                    ? theme.ERROR
                                                    : theme.INPUT_BORDER,
                                        },
                                        errors.address &&
                                            touched.address &&
                                            styles.inputError,
                                    ]}
                                >
                                    <Ionicons
                                        name="location-outline"
                                        size={22}
                                        color={
                                            errors.address && touched.address
                                                ? theme.ERROR
                                                : theme.PRIMARY
                                        }
                                        style={styles.inputIcon}
                                    />
                                    <TextInput
                                        placeholder="Enter full address"
                                        value={values.address}
                                        onChangeText={handleChange('address')}
                                        onBlur={handleBlur('address')}
                                        placeholderTextColor={
                                            theme.TEXT_PLACEHOLDER
                                        }
                                        style={[
                                            styles.input,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                        multiline
                                        accessibilityLabel="Enter address"
                                        testID="address-input"
                                    />
                                    {values.address.length > 0 && (
                                        <TouchableOpacity
                                            onPress={() =>
                                                setFieldValue('address', '')
                                            }
                                            style={styles.clearButton}
                                        >
                                            <Ionicons
                                                name="close-circle"
                                                size={20}
                                                color="#999"
                                            />
                                        </TouchableOpacity>
                                    )}
                                </View>
                                {errors.address && touched.address && (
                                    <Text
                                        style={[
                                            styles.errorText,
                                            { color: theme.ERROR },
                                        ]}
                                    >
                                        {errors.address}
                                    </Text>
                                )}
                                {/* City */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        City
                                        <Text style={{ color: theme.ERROR }}>
                                            *
                                        </Text>
                                    </Text>
                                    <View
                                        style={[
                                            styles.inputContainer,
                                            {
                                                backgroundColor:
                                                    theme.INPUT_BACKGROUND,
                                                borderColor:
                                                    errors.city && touched.city
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.city &&
                                                touched.city &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="business-outline"
                                            size={20}
                                            color={
                                                errors.city && touched.city
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    color: theme.TEXT_PRIMARY,
                                                    flex: 1,
                                                },
                                            ]}
                                            placeholder="Enter city"
                                            placeholderTextColor={
                                                theme.TEXT_PLACEHOLDER
                                            }
                                            value={values.city}
                                            onChangeText={handleChange('city')}
                                            onBlur={handleBlur('city')}
                                        />
                                    </View>
                                    {errors.city && touched.city && (
                                        <Text
                                            style={[
                                                styles.errorText,
                                                { color: theme.ERROR },
                                            ]}
                                        >
                                            {errors.city}
                                        </Text>
                                    )}
                                </View>

                                {/* State */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        State
                                        <Text style={{ color: theme.ERROR }}>
                                            *
                                        </Text>
                                    </Text>
                                    <View
                                        style={[
                                            styles.inputContainer,
                                            {
                                                backgroundColor:
                                                    theme.INPUT_BACKGROUND,
                                                borderColor:
                                                    errors.state &&
                                                    touched.state
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.state &&
                                                touched.state &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="map-outline"
                                            size={20}
                                            color={
                                                errors.state && touched.state
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    color: theme.TEXT_PRIMARY,
                                                    flex: 1,
                                                },
                                            ]}
                                            placeholder="Enter state"
                                            placeholderTextColor={
                                                theme.TEXT_PLACEHOLDER
                                            }
                                            value={values.state}
                                            onChangeText={handleChange('state')}
                                            onBlur={handleBlur('state')}
                                        />
                                    </View>
                                    {errors.state && touched.state && (
                                        <Text
                                            style={[
                                                styles.errorText,
                                                { color: theme.ERROR },
                                            ]}
                                        >
                                            {errors.state}
                                        </Text>
                                    )}
                                </View>
                                {/* Pincode */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Pincode
                                        <Text style={{ color: theme.ERROR }}>
                                            *
                                        </Text>
                                    </Text>
                                    <View
                                        style={[
                                            styles.inputContainer,
                                            {
                                                backgroundColor:
                                                    theme.INPUT_BACKGROUND,
                                                borderColor:
                                                    errors.pincode &&
                                                    touched.pincode
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.pincode &&
                                                touched.pincode &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="mail-outline"
                                            size={20}
                                            color={
                                                errors.pincode &&
                                                touched.pincode
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    color: theme.TEXT_PRIMARY,
                                                    flex: 1,
                                                },
                                            ]}
                                            placeholder="Enter pincode"
                                            placeholderTextColor={
                                                theme.TEXT_PLACEHOLDER
                                            }
                                            value={values.pincode}
                                            onChangeText={handleChange(
                                                'pincode'
                                            )}
                                            onBlur={handleBlur('pincode')}
                                            keyboardType="numeric"
                                            maxLength={6}
                                        />
                                    </View>
                                    {errors.pincode && touched.pincode && (
                                        <Text
                                            style={[
                                                styles.errorText,
                                                { color: theme.ERROR },
                                            ]}
                                        >
                                            {errors.pincode}
                                        </Text>
                                    )}
                                </View>

                                {/* Project Timeline Section */}
                                <View style={styles.sectionHeader}>
                                    <MaterialIcons
                                        name="schedule"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.sectionTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Project Timeline
                                    </Text>
                                </View>

                                {/* Expected Start Date */}
                                <View style={[styles.inputGroup]}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Expected Start Date
                                    </Text>
                                    <Pressable
                                        onPress={() =>
                                            setShowStartDatePicker(true)
                                        }
                                        style={[
                                            styles.inputContainer,
                                            {
                                                backgroundColor:
                                                    theme.INPUT_BACKGROUND,
                                                borderColor:
                                                    errors.expectedStartDate &&
                                                    touched.expectedStartDate
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.expectedStartDate &&
                                                touched.expectedStartDate &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="calendar-outline"
                                            size={20}
                                            color={
                                                errors.expectedStartDate &&
                                                touched.expectedStartDate
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <Text
                                            style={[
                                                styles.input,
                                                {
                                                    color: values.expectedStartDate
                                                        ? theme.TEXT_PRIMARY
                                                        : theme.TEXT_PLACEHOLDER,
                                                    flex: 1,
                                                },
                                            ]}
                                        >
                                            {values.expectedStartDate
                                                ? new Date(
                                                      values.expectedStartDate
                                                  ).toLocaleDateString()
                                                : 'Expected Start Date'}
                                        </Text>
                                    </Pressable>
                                    <ModalDatePicker
                                        isVisible={showStartDatePicker}
                                        mode="date"
                                        date={
                                            values.expectedStartDate
                                                ? new Date(
                                                      values.expectedStartDate
                                                  )
                                                : new Date()
                                        }
                                        onConfirm={(date) => {
                                            setFieldValue(
                                                'expectedStartDate',
                                                date
                                            );
                                            setShowStartDatePicker(false);
                                        }}
                                        onCancel={() =>
                                            setShowStartDatePicker(false)
                                        }
                                        minimumDate={new Date()}
                                        testID="startDatePicker"
                                    />
                                    {errors.expectedStartDate &&
                                        touched.expectedStartDate && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.expectedStartDate}
                                            </Text>
                                        )}
                                </View>

                                {/* Expected Completion Date */}
                                <View
                                    style={[
                                        styles.inputGroup,
                                        { flex: 1 },
                                    ]}
                                >
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Expected Completion Date
                                    </Text>
                                    <Pressable
                                        onPress={() =>
                                            setShowCompletionDatePicker(true)
                                        }
                                        style={[
                                            styles.inputContainer,
                                            {
                                                backgroundColor:
                                                    theme.INPUT_BACKGROUND,
                                                borderColor:
                                                    errors.expectedCompletionDate &&
                                                    touched.expectedCompletionDate
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.expectedCompletionDate &&
                                                touched.expectedCompletionDate &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="calendar-outline"
                                            size={20}
                                            color={
                                                errors.expectedCompletionDate &&
                                                touched.expectedCompletionDate
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <Text
                                            style={[
                                                styles.input,
                                                {
                                                    color: values.expectedCompletionDate
                                                        ? theme.TEXT_PRIMARY
                                                        : theme.TEXT_PLACEHOLDER,
                                                    flex: 1,
                                                },
                                            ]}
                                        >
                                            {values.expectedCompletionDate
                                                ? new Date(
                                                      values.expectedCompletionDate
                                                  ).toLocaleDateString()
                                                : 'Expected Completion Date'}
                                        </Text>
                                    </Pressable>
                                    <ModalDatePicker
                                        isVisible={showCompletionDatePicker}
                                        mode="date"
                                        date={
                                            values.expectedCompletionDate
                                                ? new Date(
                                                      values.expectedCompletionDate
                                                  )
                                                : new Date()
                                        }
                                        onConfirm={(date) => {
                                            setFieldValue(
                                                'expectedCompletionDate',
                                                date
                                            );
                                            setShowCompletionDatePicker(false);
                                        }}
                                        onCancel={() =>
                                            setShowCompletionDatePicker(false)
                                        }
                                        minimumDate={
                                            values.expectedStartDate
                                                ? new Date(
                                                      values.expectedStartDate
                                                  )
                                                : new Date()
                                        }
                                        testID="completionDatePicker"
                                    />
                                    {errors.expectedCompletionDate &&
                                        touched.expectedCompletionDate && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.expectedCompletionDate}
                                            </Text>
                                        )}
                                </View>
                                {/* Budget Details Section */}
                                <View style={styles.sectionHeader}>
                                    <MaterialIcons
                                        name="attach-money"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.sectionTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Budget Details
                                    </Text>
                                </View>

                                {/* Budget Range Row */}
                                <View style={styles.row}>
                                    {/* Min Budget */}
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginRight: 8 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Min Budget (₹)
                                        </Text>
                                        <View
                                            style={[
                                                styles.inputContainer,
                                                {
                                                    backgroundColor:
                                                        theme.INPUT_BACKGROUND,
                                                    borderColor:
                                                        errors.minBudget &&
                                                        touched.minBudget
                                                            ? theme.ERROR
                                                            : theme.INPUT_BORDER,
                                                },
                                                errors.minBudget &&
                                                    touched.minBudget &&
                                                    styles.inputError,
                                            ]}
                                        >
                                            <Ionicons
                                                name="cash-outline"
                                                size={20}
                                                color={
                                                    errors.minBudget &&
                                                    touched.minBudget
                                                        ? theme.ERROR
                                                        : theme.TEXT_SECONDARY
                                                }
                                                style={styles.inputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                        flex: 1,
                                                    },
                                                ]}
                                                placeholder="Enter minimum budget"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={values.minBudget}
                                                onChangeText={handleChange(
                                                    'minBudget'
                                                )}
                                                onBlur={handleBlur('minBudget')}
                                                keyboardType="numeric"
                                            />
                                        </View>
                                        {errors.minBudget &&
                                            touched.minBudget && (
                                                <Text
                                                    style={[
                                                        styles.errorText,
                                                        { color: theme.ERROR },
                                                    ]}
                                                >
                                                    {errors.minBudget}
                                                </Text>
                                            )}
                                    </View>

                                    {/* Max Budget */}
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginLeft: 8 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Max Budget (₹)
                                        </Text>
                                        <View
                                            style={[
                                                styles.inputContainer,
                                                {
                                                    backgroundColor:
                                                        theme.INPUT_BACKGROUND,
                                                    borderColor:
                                                        errors.maxBudget &&
                                                        touched.maxBudget
                                                            ? theme.ERROR
                                                            : theme.INPUT_BORDER,
                                                },
                                                errors.maxBudget &&
                                                    touched.maxBudget &&
                                                    styles.inputError,
                                            ]}
                                        >
                                            <Ionicons
                                                name="wallet-outline"
                                                size={20}
                                                color={
                                                    errors.maxBudget &&
                                                    touched.maxBudget
                                                        ? theme.ERROR
                                                        : theme.TEXT_SECONDARY
                                                }
                                                style={styles.inputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                        flex: 1,
                                                    },
                                                ]}
                                                placeholder="Enter maximum budget"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={values.maxBudget}
                                                onChangeText={handleChange(
                                                    'maxBudget'
                                                )}
                                                onBlur={handleBlur('maxBudget')}
                                                keyboardType="numeric"
                                            />
                                        </View>
                                        {errors.maxBudget &&
                                            touched.maxBudget && (
                                                <Text
                                                    style={[
                                                        styles.errorText,
                                                        { color: theme.ERROR },
                                                    ]}
                                                >
                                                    {errors.maxBudget}
                                                </Text>
                                            )}
                                    </View>
                                </View>
                                <View style={styles.sectionHeader}>
                                    <MaterialIcons
                                        name="home"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.sectionTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Design Preferences
                                    </Text>
                                </View>
                                <View style={styles.row}>
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginRight: 4 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Floors
                                        </Text>
                                        <View
                                            style={[
                                                styles.inputContainer,
                                                {
                                                    backgroundColor:
                                                        theme.INPUT_BACKGROUND,
                                                    borderColor:
                                                        errors.floors &&
                                                        touched.floors
                                                            ? theme.ERROR
                                                            : theme.INPUT_BORDER,
                                                },
                                                errors.floors &&
                                                    touched.floors &&
                                                    styles.inputError,
                                            ]}
                                        >
                                            <Ionicons
                                                name="layers-outline"
                                                size={20}
                                                color={
                                                    errors.floors &&
                                                    touched.floors
                                                        ? theme.ERROR
                                                        : theme.TEXT_SECONDARY
                                                }
                                                style={styles.inputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                        flex: 1,
                                                    },
                                                ]}
                                                placeholder="Number of floors"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={values.floors}
                                                onChangeText={handleChange(
                                                    'floors'
                                                )}
                                                onBlur={handleBlur('floors')}
                                                keyboardType="numeric"
                                            />
                                        </View>
                                        {errors.floors && touched.floors && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.floors}
                                            </Text>
                                        )}
                                    </View>
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginHorizontal: 4 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Bedrooms
                                        </Text>
                                        <View
                                            style={[
                                                styles.inputContainer,
                                                {
                                                    backgroundColor:
                                                        theme.INPUT_BACKGROUND,
                                                    borderColor:
                                                        errors.bedrooms &&
                                                        touched.bedrooms
                                                            ? theme.ERROR
                                                            : theme.INPUT_BORDER,
                                                },
                                                errors.bedrooms &&
                                                    touched.bedrooms &&
                                                    styles.inputError,
                                            ]}
                                        >
                                            <Ionicons
                                                name="bed-outline"
                                                size={20}
                                                color={
                                                    errors.bedrooms &&
                                                    touched.bedrooms
                                                        ? theme.ERROR
                                                        : theme.TEXT_SECONDARY
                                                }
                                                style={styles.inputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                        flex: 1,
                                                    },
                                                ]}
                                                placeholder="Number of bedrooms"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={values.bedrooms}
                                                onChangeText={handleChange(
                                                    'bedrooms'
                                                )}
                                                onBlur={handleBlur('bedrooms')}
                                                keyboardType="numeric"
                                            />
                                        </View>
                                        {errors.bedrooms &&
                                            touched.bedrooms && (
                                                <Text
                                                    style={[
                                                        styles.errorText,
                                                        { color: theme.ERROR },
                                                    ]}
                                                >
                                                    {errors.bedrooms}
                                                </Text>
                                            )}
                                    </View>
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginLeft: 4 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Bathrooms
                                        </Text>
                                        <View
                                            style={[
                                                styles.inputContainer,
                                                {
                                                    backgroundColor:
                                                        theme.INPUT_BACKGROUND,
                                                    borderColor:
                                                        errors.bathrooms &&
                                                        touched.bathrooms
                                                            ? theme.ERROR
                                                            : theme.INPUT_BORDER,
                                                },
                                                errors.bathrooms &&
                                                    touched.bathrooms &&
                                                    styles.inputError,
                                            ]}
                                        >
                                            <MaterialIcons
                                                name="bathroom"
                                                size={20}
                                                color={
                                                    errors.bathrooms &&
                                                    touched.bathrooms
                                                        ? theme.ERROR
                                                        : theme.TEXT_SECONDARY
                                                }
                                                style={styles.inputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                        flex: 1,
                                                    },
                                                ]}
                                                placeholder="Number of bathrooms"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={values.bathrooms}
                                                onChangeText={handleChange(
                                                    'bathrooms'
                                                )}
                                                onBlur={handleBlur('bathrooms')}
                                                keyboardType="numeric"
                                            />
                                        </View>
                                        {errors.bathrooms &&
                                            touched.bathrooms && (
                                                <Text
                                                    style={[
                                                        styles.errorText,
                                                        { color: theme.ERROR },
                                                    ]}
                                                >
                                                    {errors.bathrooms}
                                                </Text>
                                            )}
                                    </View>
                                </View>

                                {/* Preferences Switches */}
                                <View style={styles.switchContainer}>
                                    <View style={styles.switchRow}>
                                        <View
                                            style={styles.switchLabelContainer}
                                        >
                                            <Text
                                                style={[
                                                    styles.switchLabel,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                    },
                                                ]}
                                            >
                                                Parking Required
                                            </Text>
                                            <Text
                                                style={[
                                                    styles.switchDescription,
                                                    {
                                                        color: theme.TEXT_SECONDARY,
                                                    },
                                                ]}
                                            >
                                                Include dedicated parking space
                                            </Text>
                                        </View>
                                        <Switch
                                            value={values.parkingRequired}
                                            onValueChange={(value) =>
                                                setFieldValue(
                                                    'parkingRequired',
                                                    value
                                                )
                                            }
                                            trackColor={{
                                                false: theme.INPUT_BORDER,
                                                true: theme.PRIMARY,
                                            }}
                                            thumbColor={
                                                values.parkingRequired
                                                    ? theme.WHITE
                                                    : theme.TEXT_SECONDARY
                                            }
                                        />
                                    </View>

                                    <View style={styles.switchRow}>
                                        <View
                                            style={styles.switchLabelContainer}
                                        >
                                            <Text
                                                style={[
                                                    styles.switchLabel,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                    },
                                                ]}
                                            >
                                                Garden Required
                                            </Text>
                                            <Text
                                                style={[
                                                    styles.switchDescription,
                                                    {
                                                        color: theme.TEXT_SECONDARY,
                                                    },
                                                ]}
                                            >
                                                Include garden or landscaping
                                                area
                                            </Text>
                                        </View>
                                        <Switch
                                            value={values.gardenRequired}
                                            onValueChange={(value) =>
                                                setFieldValue(
                                                    'gardenRequired',
                                                    value
                                                )
                                            }
                                            trackColor={{
                                                false: theme.INPUT_BORDER,
                                                true: theme.PRIMARY,
                                            }}
                                            thumbColor={
                                                values.gardenRequired
                                                    ? theme.WHITE
                                                    : theme.TEXT_SECONDARY
                                            }
                                        />
                                    </View>

                                    <View style={styles.switchRow}>
                                        <View
                                            style={styles.switchLabelContainer}
                                        >
                                            <Text
                                                style={[
                                                    styles.switchLabel,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                    },
                                                ]}
                                            >
                                                Vastu Compliance
                                            </Text>
                                            <Text
                                                style={[
                                                    styles.switchDescription,
                                                    {
                                                        color: theme.TEXT_SECONDARY,
                                                    },
                                                ]}
                                            >
                                                Follow traditional Vastu
                                                principles
                                            </Text>
                                        </View>
                                        <Switch
                                            value={values.vastuCompliance}
                                            onValueChange={(value) =>
                                                setFieldValue(
                                                    'vastuCompliance',
                                                    value
                                                )
                                            }
                                            trackColor={{
                                                false: theme.INPUT_BORDER,
                                                true: theme.PRIMARY,
                                            }}
                                            thumbColor={
                                                values.vastuCompliance
                                                    ? theme.WHITE
                                                    : theme.TEXT_SECONDARY
                                            }
                                        />
                                    </View>

                                    <View style={styles.switchRow}>
                                        <View
                                            style={styles.switchLabelContainer}
                                        >
                                            <Text
                                                style={[
                                                    styles.switchLabel,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                    },
                                                ]}
                                            >
                                                Broker Assistance Required
                                            </Text>
                                            <Text
                                                style={[
                                                    styles.switchDescription,
                                                    {
                                                        color: theme.TEXT_SECONDARY,
                                                    },
                                                ]}
                                            >
                                                Get help from real estate
                                                brokers
                                            </Text>
                                        </View>
                                        <Switch
                                            value={
                                                values.brokerAssistanceRequired
                                            }
                                            onValueChange={(value) =>
                                                setFieldValue(
                                                    'brokerAssistanceRequired',
                                                    value
                                                )
                                            }
                                            trackColor={{
                                                false: theme.INPUT_BORDER,
                                                true: theme.PRIMARY,
                                            }}
                                            thumbColor={
                                                values.brokerAssistanceRequired
                                                    ? theme.WHITE
                                                    : theme.TEXT_SECONDARY
                                            }
                                        />
                                    </View>
                                </View>

                                {/* Additional Details Section - Modern Design */}
                                <View
                                    style={[
                                        styles.modernSectionContainer,
                                        { backgroundColor: theme.CARD + '40' },
                                    ]}
                                >
                                    <View style={styles.modernSectionHeader}>
                                        <View
                                            style={[
                                                styles.iconContainer,
                                                {
                                                    backgroundColor:
                                                        theme.PRIMARY + '15',
                                                },
                                            ]}
                                        >
                                            <MaterialIcons
                                                name="auto-awesome"
                                                size={24}
                                                color={theme.PRIMARY}
                                            />
                                        </View>
                                        <View
                                            style={styles.headerTextContainer}
                                        >
                                            <Text
                                                style={[
                                                    styles.modernSectionTitle,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                    },
                                                ]}
                                            >
                                                Personalize Your Project
                                            </Text>
                                            <Text
                                                style={[
                                                    styles.modernSectionSubtitle,
                                                    {
                                                        color: theme.TEXT_SECONDARY,
                                                    },
                                                ]}
                                            >
                                                Add the finishing touches that
                                                make it uniquely yours
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                                
                                {/* Submit Button */}
                                <TouchableOpacity
                                    style={[
                                        styles.submitButton,
                                        {
                                            borderColor: theme.PRIMARY,
                                            opacity:
                                                createProjectMutation.isPending
                                                    ? 0.7
                                                    : 1,
                                        },
                                    ]}
                                    onPress={handleSubmit}
                                    disabled={createProjectMutation.isPending}
                                    activeOpacity={0.8}
                                >
                                    <LinearGradient
                                        colors={[
                                            theme.PRIMARY,
                                            theme.SECONDARY,
                                        ]}
                                        start={{ x: 0, y: 0 }}
                                        end={{ x: 1, y: 0 }}
                                        style={styles.submitButtonGradient}
                                    >
                                        <Text
                                            style={[
                                                styles.submitText,
                                                { color: theme.WHITE },
                                            ]}
                                        >
                                            {createProjectMutation.isPending
                                                ? 'Creating...'
                                                : 'Create Project'}
                                        </Text>
                                    </LinearGradient>
                                </TouchableOpacity>
                            </Animated.View>
                        </ScrollView>
                    )}
                </Formik>
                {createProjectMutation.isPending && (
                    <View style={styles.scanningOverlay}>
                        <View style={styles.scanningContainer}>
                            <ActivityIndicator
                                size="large"
                                color={theme.PRIMARY}
                            />
                            <Text
                                style={[
                                    styles.scanningText,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Creating Project...
                            </Text>
                        </View>
                    </View>
                )}
            </SafeAreaView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: { flex: 1 },
    scrollContainer: {
        flexGrow: 1,
        justifyContent: 'center',
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.6,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    contentContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: height * 0.1,
    },
    safeArea: { flex: 1 },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    scrollView: { flex: 1 },
    formContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 24,
        marginHorizontal: 20,
        alignSelf: 'center',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 14,
        marginBottom: 24,
        textAlign: 'center',
    },
    inputGroup: {
        marginBottom: 8,
    },
    label: {
        fontSize: 12,
        fontWeight: '500',
        marginBottom: 8,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        marginBottom: 4,
        paddingHorizontal: 10,
        height: 56,
    },
    addressInputContainer: {
        height: 80,
    },
    inputIcon: {
        marginRight: 12,
    },
    clearButton: {
        padding: 8,
    },
    input: {
        flex: 1,
        height: '100%',
        fontSize: 14,
    },
    inputError: {
        borderColor: 'red',
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 24,
        marginBottom: 16,
    },
    sectionTitle: {
        fontSize: 14,
        fontWeight: '600',
        marginLeft: 8,
    },
    sectionTitleContainer: {
        flex: 1,
        marginLeft: 8,
    },
    sectionSubtitle: {
        fontSize: 12,
        marginTop: 2,
        lineHeight: 16,
    },
    row: {
        flexDirection: 'row',
    },
    errorText: { fontSize: 12, marginTop: 4 },
    submitButton: {
        borderRadius: 12,
        overflow: 'hidden',
        borderWidth: 2,
        marginTop: 32,
        marginBottom: 8,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    submitButtonGradient: {
        paddingVertical: 14,
        alignItems: 'center',
    },
    submitText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    switchContainer: {
        marginTop: 16,
    },
    switchRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 12,
    },
    switchLabel: {
        fontSize: 14,
        fontWeight: '500',
    },
    switchLabelContainer: {
        flex: 1,
        marginRight: 12,
    },
    switchDescription: {
        fontSize: 12,
        marginTop: 2,
        lineHeight: 16,
    },
    scanningOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    scanningContainer: {
        backgroundColor: 'white',
        borderRadius: 20,
        padding: 30,
        alignItems: 'center',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 10,
        elevation: 10,
    },
    scanningText: {
        fontSize: 16,
        fontWeight: '600',
        marginTop: 16,
        textAlign: 'center',
    },
    submitButtonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    // Modern Design Styles
    modernSectionContainer: {
        borderRadius: 16,
        padding: 20,
        marginBottom: 24,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3,
    },
    modernSectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    iconContainer: {
        width: 48,
        height: 48,
        borderRadius: 24,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    headerTextContainer: {
        flex: 1,
    },
    modernSectionTitle: {
        fontSize: 18,
        fontWeight: '700',
        marginBottom: 4,
    },
    modernSectionSubtitle: {
        fontSize: 14,
        lineHeight: 20,
    },
    picker: {
        flex: 1,
        height: 56,
        fontSize: 14,
    },
});
